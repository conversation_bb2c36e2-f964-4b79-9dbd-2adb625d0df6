# Sevalla Production Environment Variables
# Replace these with your actual production values
BOT_CLIENT_ID="1394521471862308884"
BOT_CLIENT_SECRET="[YOUR_ACTUAL_SECRET]"
APP_URL="https://discordbot-energex-jkhvk.sevalla.app"
NEXT_PUBLIC_API_ENDPOINT="https://discordbot-energex-backend.sevalla.app"
INTERNAL_API_ENDPOINT="https://discordbot-energex-backend.sevalla.app"
NODE_ENV="production"
# Bot Configuration
BOT_CLIENT_ID="YOUR_PRODUCTION_BOT_CLIENT_ID"
BOT_CLIENT_SECRET="YOUR_PRODUCTION_BOT_CLIENT_SECRET"
NEXT_PUBLIC_BOT_CLIENT_ID="YOUR_PRODUCTION_BOT_CLIENT_ID"

# Application URLs (Updated with actual Sevalla deployment URLs)
APP_URL="https://discordbot-energex-jkhvk.sevalla.app"
NEXT_PUBLIC_API_ENDPOINT="https://discordbot-energex-backend.sevalla.app"
INTERNAL_API_ENDPOINT="https://discordbot-energex-backend.sevalla.app"

# Sevalla-specific environment variables
NODE_ENV="production"
PORT="3000"

# Disable development features in production
ENABLE_ENV_LOGIN="false"

# Additional production configurations
NEXT_TELEMETRY_DISABLED="1"